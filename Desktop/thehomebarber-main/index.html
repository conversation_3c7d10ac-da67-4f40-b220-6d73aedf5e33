<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      The Home Barber - Professional Mobile Barber Service in Colorado | At-Home
      Haircuts
    </title>

    <!-- Primary Meta Tags -->
    <meta
      name="title"
      content="The Home Barber - Professional Mobile Barber Service in Colorado | At-Home Haircuts"
    />
    <meta
      name="description"
      content="Experience premium mobile barbering services in Colorado. Professional haircuts, beard trims, and grooming at your doorstep. Book your at-home barber service today!"
    />
    <meta
      name="keywords"
      content="mobile barber, home barber service, at-home haircuts, mobile barbering, professional barber, Colorado barber, Denver barber, beard trim, men's haircuts, children's haircuts, home grooming service"
    />
    <meta name="author" content="The Home Barber" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="geo.region" content="US-CO" />
    <meta name="geo.placename" content="Denver" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://thehomebarber.com/" />
    <meta
      property="og:title"
      content="The Home Barber - Professional Mobile Barber Service in Colorado"
    />
    <meta
      property="og:description"
      content="Experience premium mobile barbering services in Colorado. Professional haircuts, beard trims, and grooming at your doorstep. Book your at-home barber service today!"
    />
    <meta
      property="og:image"
      content="https://public.readdy.ai/ai/img_res/efe499a734e495000aa4fed0f5a6ccb4.jpg"
    />
    <meta property="og:locale" content="en_US" />
    <meta property="og:site_name" content="The Home Barber" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://thehomebarber.com/" />
    <meta
      property="twitter:title"
      content="The Home Barber - Professional Mobile Barber Service in Colorado"
    />
    <meta
      property="twitter:description"
      content="Experience premium mobile barbering services in Colorado. Professional haircuts, beard trims, and grooming at your doorstep. Book your at-home barber service today!"
    />
    <meta
      property="twitter:image"
      content="https://public.readdy.ai/ai/img_res/efe499a734e495000aa4fed0f5a6ccb4.jpg"
    />

    <!-- Additional SEO Tags -->
    <link rel="canonical" href="https://thehomebarber.com/" />
    <meta name="format-detection" content="telephone=yes" />
    <meta name="theme-color" content="#1E3D59" />

    <!-- Schema.org markup for Google+ -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "The Home Barber",
        "image": "https://public.readdy.ai/ai/img_res/efe499a734e495000aa4fed0f5a6ccb4.jpg",
        "description": "Professional mobile barbering services in Colorado. We bring the salon experience to your doorstep.",
        "@id": "https://thehomebarber.com",
        "url": "https://thehomebarber.com",

        "email": "<EMAIL>",
        "priceRange": "$5-$30",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Based in Denver",
          "addressLocality": "Denver",
          "addressRegion": "CO",
          "postalCode": "80202",
          "addressCountry": "US"
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": 39.7392,
          "longitude": -104.9903
        },
        "areaServed": [
          "Denver",
          "Boulder",
          "Fort Collins",
          "Colorado Springs",
          "Aurora",
          "Lakewood"
        ],
        "openingHoursSpecification": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"
          ],
          "opens": "08:00",
          "closes": "20:00"
        },
        "sameAs": [
          "https://facebook.com/thehomebarber",
          "https://instagram.com/thehomebarber",
          "https://twitter.com/thehomebarber"
        ]
      }
    </script>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Updated with modern fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@700&display=swap"
      rel="stylesheet"
    />
    <!-- Font Awesome 6 (Modern Icons) -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <!-- Slick Carousel CSS -->
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"
    />
    <!-- AOS Animation CSS -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <!-- jQuery (required by Slick Carousel) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Slick Carousel JS -->
    <script
      type="text/javascript"
      src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"
    ></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            colors: {
              primary: "#3A5199", // Updated modern blue
              secondary: "#FF6B6B", // Updated vibrant accent color
              tertiary: "#4ECDC4", // New teal accent color
              dark: "#2D3748", // Dark shade for text
              light: "#F7FAFC", // Light shade for backgrounds
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
            },
            boxShadow: {
              custom:
                "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
              card: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)",
              hover:
                "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
            },
          },
        },
      };

      // Dark mode initialization: Check localStorage or system preference.
      if (
        localStorage.getItem("theme") === "dark" ||
        (!localStorage.getItem("theme") &&
          window.matchMedia("(prefers-color-scheme: dark)").matches)
      ) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }

      // Toggle dark mode and update localStorage and toggle icon.
      function toggleDarkMode() {
        document.documentElement.classList.toggle("dark");
        const toggleIcon = document.getElementById("darkModeIcon");
        const toggleIconMobile = document.getElementById("darkModeIconMobile");
        if (document.documentElement.classList.contains("dark")) {
          localStorage.setItem("theme", "dark");
          toggleIcon.className = "fa-solid fa-sun text-xl";
          toggleIconMobile.className = "fa-solid fa-sun text-xl";
        } else {
          localStorage.setItem("theme", "light");
          toggleIcon.className = "fa-solid fa-moon text-xl";
          toggleIconMobile.className = "fa-solid fa-moon text-xl";
        }
      }
    </script>
    <style>
      /* Set the proper font families loaded from Google Fonts */
      html,
      body {
        font-family: "Poppins", sans-serif;
        scroll-behavior: smooth;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-family: "Montserrat", sans-serif;
      }

      /* Apply the Playfair Display font to elements with the logo class */
      .logo,
      .logo a {
        font-family: "Playfair Display", serif;
        letter-spacing: 0.5px;
      }

      /* Custom rounded-button class with modern styling */
      .rounded-button {
        border-radius: 8px !important;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .rounded-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -4px rgba(0, 0, 0, 0.1);
      }

      .rounded-button::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: all 0.3s ease;
      }

      .rounded-button:hover::after {
        opacity: 1;
      }

      /* Card hover effects with enhanced animations */
      .service-card {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -2px rgba(0, 0, 0, 0.1);
      }

      .service-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 8px 10px -6px rgba(0, 0, 0, 0.1);
      }

      .testimonial-card {
        width: 300px;
        height: 280px;
        position: relative;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -2px rgba(0, 0, 0, 0.1);
      }

      .testimonial-card:hover {
        transform: scale(1.03);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 8px 10px -6px rgba(0, 0, 0, 0.1);
      }

      .testimonial-text {
        position: relative;
        max-height: 4.5em;
        overflow: hidden;
        margin-bottom: 1.5rem;
        line-height: 1.5em;
      }

      .testimonial-text.has-tooltip {
        cursor: pointer;
      }

      .testimonial-text.has-tooltip p {
        display: inline;
      }

      .testimonial-text.has-tooltip:after {
        content: "...";
        position: absolute;
        bottom: 0;
        right: 0;
        background: white;
        padding: 0 4px;
      }

      .dark .testimonial-text.has-tooltip:after {
        background: #374151;
      }

      .testimonial-tooltip {
        visibility: hidden;
        position: absolute;
        z-index: 100;
        background-color: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        width: 300px;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        transition: opacity 0.3s;
      }

      .testimonial-text.has-tooltip:hover .testimonial-tooltip {
        visibility: visible;
        opacity: 1;
      }

      .testimonial-author {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
      }

      /* Modal overlay with modern glass effect */
      #confirmationModal,
      #successModal {
        backdrop-filter: blur(8px);
      }

      #confirmationModal .bg-white,
      #successModal .bg-white {
        border-radius: 16px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      }

      /* Slick Carousel custom styling */
      .center .slick-slide {
        filter: none;
        opacity: 1;
        transition: all 0.3s ease;
        margin: 0 15px;
      }

      .slick-list {
        margin: 0 -15px;
        padding: 30px 0;
      }

      .slick-prev:before,
      .slick-next:before {
        color: #3a5199;
        font-size: 24px;
      }

      .slick-dots li button:before {
        font-size: 12px;
        color: #3a5199;
      }

      /* Dark mode adjustments with smoother transitions */
      .dark .text-gray-600 {
        color: #e5e7eb;
      }

      .dark .service-card .text-gray-600,
      .dark .pricing-card .text-gray-600,
      .dark .testimonial-card .text-gray-600 {
        color: #e5e7eb;
      }

      .dark .fa-check-circle,
      .dark .fa-phone,
      .dark .fa-envelope,
      .dark .fa-clock,
      .dark .fa-location-dot {
        color: #e5e7eb;
      }

      .dark .text-primary {
        color: #60a5fa;
      }

      .dark .bg-primary\/10 {
        background-color: rgba(96, 165, 250, 0.1);
      }

      .dark .pricing-card {
        background-color: #1f2937;
      }

      .dark .service-icon {
        color: #60a5fa;
      }

      .dark input,
      .dark select,
      .dark textarea {
        background-color: #374151;
        color: #e5e7eb;
        border-color: #4b5563;
      }

      .dark input::placeholder,
      .dark textarea::placeholder {
        color: #9ca3af;
      }

      .dark #confirmationModal .bg-white,
      .dark #successModal .bg-white {
        background-color: #1f2937;
      }

      .dark #confirmationModal .text-gray-600,
      .dark #successModal .text-gray-600 {
        color: #e5e7eb;
      }

      .dark .testimonial-card {
        background-color: #1f2937;
      }

      .dark .testimonial-card .text-gray-600 {
        color: #e5e7eb;
      }

      /* Skip link for accessibility */
      .skip-link {
        position: absolute;
        left: -999px;
        top: auto;
        width: 1px;
        height: 1px;
        overflow: hidden;
      }

      .skip-link:focus {
        left: 0;
        top: 0;
        width: auto;
        height: auto;
        background: #3a5199;
        color: white;
        padding: 0.5rem;
        z-index: 1000;
      }

      /* Booking Step Hover Animation with enhanced effects */
      .booking-step {
        transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275),
          box-shadow 0.4s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
      }

      .booking-step:hover {
        transform: scale(1.05);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 8px 10px -6px rgba(0, 0, 0, 0.1);
      }

      /* Modern gradient backgrounds */
      .gradient-bg {
        background: linear-gradient(135deg, #3a5199 0%, #4ecdc4 100%);
      }

      .gradient-text {
        background: linear-gradient(135deg, #3a5199 0%, #4ecdc4 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }

      /* Modern service icon styling */
      .service-icon-container {
        position: relative;
        z-index: 1;
      }

      .service-icon-container::before {
        content: "";
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: linear-gradient(135deg, #3a5199 0%, #4ecdc4 100%);
        border-radius: 50%;
        z-index: -1;
        opacity: 0.2;
        transition: all 0.3s ease;
      }

      .service-card:hover .service-icon-container::before {
        opacity: 0.4;
        transform: scale(1.1);
      }

      /* Modern pricing card styling */
      .pricing-card {
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      }

      .pricing-card::before {
        content: "";
        position: absolute;
        width: 200%;
        height: 200%;
        top: -50%;
        left: -50%;
        z-index: 0;
        background: linear-gradient(
          135deg,
          rgba(58, 81, 153, 0.05) 0%,
          rgba(78, 205, 196, 0.05) 100%
        );
        transform: rotate(30deg);
        transition: all 0.4s ease;
      }

      .pricing-card:hover::before {
        transform: rotate(20deg) scale(1.1);
      }

      .pricing-card > * {
        position: relative;
        z-index: 1;
      }

      /* Modern form styling */
      input,
      select,
      textarea {
        transition: all 0.3s ease;
      }

      input:focus,
      select:focus,
      textarea:focus {
        transform: translateY(-2px);
      }

      /* Animated underline for navigation links */
      .nav-link {
        position: relative;
      }

      .nav-link::after {
        content: "";
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -4px;
        left: 0;
        background: linear-gradient(90deg, #3a5199 0%, #4ecdc4 100%);
        transition: width 0.3s ease;
      }

      .nav-link:hover::after {
        width: 100%;
      }

      /* Modern scrollbar */
      ::-webkit-scrollbar {
        width: 10px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: #3a5199;
        border-radius: 5px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #4ecdc4;
      }

      /* Dark mode scrollbar */
      .dark ::-webkit-scrollbar-track {
        background: #2d3748;
      }

      .dark ::-webkit-scrollbar-thumb {
        background: #60a5fa;
      }

      .dark ::-webkit-scrollbar-thumb:hover {
        background: #4ecdc4;
      }
    </style>
  </head>
  <body class="bg-white dark:bg-gray-900 text-black dark:text-white">
    <!-- Skip to content link for keyboard users -->
    <a href="#main-content" class="skip-link">Skip to content</a>

    <!-- Navbar -->
    <nav
      role="navigation"
      aria-label="Primary Navigation"
      class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-800 shadow-md backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-20">
          <!-- Logo -->
          <div class="flex items-center">
            <a href="#home" class="logo text-3xl font-bold gradient-text"
              >The Home Barber</a
            >
          </div>
          <!-- Desktop Navigation & Controls -->
          <div class="hidden md:flex items-center space-x-8">
            <a
              href="#home"
              class="text-gray-700 dark:text-white hover:text-primary nav-link font-medium"
              >Home</a
            >
            <a
              href="#services"
              class="text-gray-700 dark:text-white hover:text-primary nav-link font-medium"
              >Services</a
            >
            <a
              href="#pricing"
              class="text-gray-700 dark:text-white hover:text-primary nav-link font-medium"
              >Pricing</a
            >
            <a
              href="#contact"
              class="text-gray-700 dark:text-white hover:text-primary nav-link font-medium"
              >Contact</a
            >
            <button
              onclick="document.getElementById('booking').scrollIntoView({ behavior: 'smooth' })"
              class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap font-medium shadow-md"
            >
              Book Now
            </button>
            <!-- Dark Mode Toggle -->
            <button
              id="darkModeToggle"
              onclick="toggleDarkMode()"
              aria-label="Toggle dark mode"
              class="text-gray-700 dark:text-white focus:outline-none ml-4 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300"
            >
              <i id="darkModeIcon" class="fa-solid fa-moon text-xl"></i>
            </button>
          </div>
          <!-- Mobile Controls -->
          <div class="md:hidden flex items-center space-x-4">
            <button
              id="darkModeToggleMobile"
              onclick="toggleDarkMode()"
              aria-label="Toggle dark mode"
              class="text-gray-700 dark:text-white focus:outline-none p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300"
            >
              <i id="darkModeIconMobile" class="fa-solid fa-moon text-xl"></i>
            </button>
            <button
              id="mobileMenuButton"
              class="text-gray-700 dark:text-white focus:outline-none p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300"
              aria-label="Toggle navigation"
              aria-expanded="false"
              aria-controls="mobileMenu"
            >
              <svg
                class="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <!-- Mobile Navigation Menu -->
      <div
        id="mobileMenu"
        class="hidden md:hidden bg-white dark:bg-gray-800 shadow-lg rounded-b-lg"
      >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex flex-col items-center space-y-6 py-6">
            <a
              href="#home"
              class="block text-center text-gray-700 dark:text-white hover:text-primary font-medium"
              >Home</a
            >
            <a
              href="#services"
              class="block text-center text-gray-700 dark:text-white hover:text-primary font-medium"
              >Services</a
            >
            <a
              href="#pricing"
              class="block text-center text-gray-700 dark:text-white hover:text-primary font-medium"
              >Pricing</a
            >
            <a
              href="#contact"
              class="block text-center text-gray-700 dark:text-white hover:text-primary font-medium"
              >Contact</a
            >
            <button
              onclick="document.getElementById('booking').scrollIntoView({ behavior: 'smooth' })"
              class="w-full bg-primary text-white px-6 py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap font-medium shadow-md"
            >
              Book Now
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="pt-16">
      <!-- Hero Section -->
      <section
        id="home"
        class="relative h-[700px] bg-cover bg-center bg-fixed"
        style="
          background-image: url('https://public.readdy.ai/ai/img_res/efe499a734e495000aa4fed0f5a6ccb4.jpg');
        "
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-black/70 to-black/50"
        ></div>
        <div
          class="relative max-w-7xl mx-auto w-full px-4 lg:px-8 h-full flex items-center"
        >
          <div
            class="max-w-2xl text-white text-center md:text-left"
            data-aos="fade-right"
            data-aos-delay="200"
          >
            <h1 class="text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Professional Haircuts at Your Doorstep
            </h1>
            <p class="text-xl mb-8 text-gray-200">
              Experience premium barbering services in the comfort of your home.
              Our skilled barbers bring the salon experience to you.
            </p>
            <div
              class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 md:justify-start"
            >
              <button
                onclick="document.getElementById('booking').scrollIntoView({ behavior: 'smooth' })"
                class="bg-white text-primary px-6 py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap font-semibold shadow-lg transform transition-all duration-300 hover:scale-105"
              >
                Book Appointment
              </button>
              <button
                onclick="document.getElementById('services').scrollIntoView({ behavior: 'smooth' })"
                class="border-2 border-white text-white px-6 py-3 rounded-lg hover:bg-white hover:text-primary whitespace-nowrap font-semibold transform transition-all duration-300 hover:scale-105"
              >
                View Services
              </button>
            </div>
          </div>
        </div>
        <div class="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120">
            <path
              fill="#f9fafb"
              fill-opacity="1"
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            ></path>
          </svg>
        </div>
      </section>

      <!-- Services Section -->
      <section
        id="services"
        class="py-24 bg-gray-50 dark:bg-gray-700"
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-20">
            <span
              class="text-sm font-semibold text-primary uppercase tracking-wider"
              >What We Offer</span
            >
            <h2
              class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mt-2 mb-4"
            >
              Professional Services
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mb-6 rounded-full"></div>
            <p class="text-gray-600 dark:text-white max-w-2xl mx-auto text-lg">
              Experience our expert grooming services delivered with precision
              and care.
            </p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-card transition-all duration-300 service-card text-center"
              data-aos="fade-up"
              data-aos-delay="0"
            >
              <div
                class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 service-icon-container"
              >
                <i class="fa-solid fa-scissors text-primary text-2xl"></i>
              </div>
              <h3 class="text-xl font-bold mb-4">Classic Haircut</h3>
              <p class="text-gray-600 dark:text-white">
                Professional men's haircut with expert styling and finish.
              </p>
            </div>
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-card transition-all duration-300 service-card text-center"
              data-aos="fade-up"
              data-aos-delay="100"
            >
              <div
                class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 service-icon-container"
              >
                <i class="fa-solid fa-child text-primary text-2xl"></i>
              </div>
              <h3 class="text-xl font-bold mb-4">Children's Cut</h3>
              <p class="text-gray-600 dark:text-white">
                Gentle and patient service for our younger clients.
              </p>
            </div>
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-card transition-all duration-300 service-card text-center"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              <div
                class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 service-icon-container"
              >
                <i class="fas fa-cut text-primary text-2xl"></i>
              </div>
              <h3 class="text-xl font-bold mb-4">Beard Trim</h3>
              <p class="text-gray-600 dark:text-white">
                Expert beard shaping and styling for a polished look.
              </p>
            </div>
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-card transition-all duration-300 service-card text-center"
              data-aos="fade-up"
              data-aos-delay="300"
            >
              <div
                class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 service-icon-container"
              >
                <i
                  class="fa-solid fa-spray-can-sparkles text-primary text-2xl"
                ></i>
              </div>
              <h3 class="text-xl font-bold mb-4">Clean Shave</h3>
              <p class="text-gray-600 dark:text-white">
                Traditional straight razor shave with hot towel service.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Pricing Section -->
      <section
        id="pricing"
        class="py-24 bg-white dark:bg-gray-900"
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-20">
            <span
              class="text-sm font-semibold text-primary uppercase tracking-wider"
              >Our Rates</span
            >
            <h2
              class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mt-2 mb-4"
            >
              Simple Pricing
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mb-6 rounded-full"></div>
            <p class="text-gray-600 dark:text-white max-w-2xl mx-auto text-lg">
              Transparent pricing for all our premium grooming services.
            </p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-card hover:shadow-hover transition-all duration-300 pricing-card"
              data-aos="fade-up"
              data-aos-delay="0"
            >
              <div class="mb-8">
                <h3 class="text-2xl font-bold mb-2 text-left">Basic Cut</h3>
                <div class="text-4xl font-bold gradient-text mb-6 text-left">
                  $25
                </div>
                <ul class="text-gray-600 dark:text-white space-y-4 text-left">
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Classic Children's Haircut</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Basic Styling</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Neck Trim</span>
                  </li>
                </ul>
              </div>
              <button
                onclick="document.getElementById('booking').scrollIntoView({ behavior: 'smooth' })"
                class="w-full bg-primary text-white py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap shadow-md font-medium"
              >
                Book Now
              </button>
            </div>
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-custom hover:shadow-hover transition-all duration-300 pricing-card relative transform scale-105 z-10"
              data-aos="fade-up"
              data-aos-delay="100"
            >
              <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span
                  class="bg-gradient-to-r from-primary to-tertiary text-white px-6 py-2 rounded-full text-sm font-bold shadow-md"
                  >Most Popular</span
                >
              </div>
              <div class="mb-8">
                <h3 class="text-2xl font-bold mb-2 text-left">
                  Premium Package
                </h3>
                <div class="text-4xl font-bold gradient-text mb-6 text-left">
                  $30
                </div>
                <ul class="text-gray-600 dark:text-white space-y-4 text-left">
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Classic Haircut</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Premium Styling</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Beard Trim</span>
                  </li>
                </ul>
              </div>
              <button
                onclick="document.getElementById('booking').scrollIntoView({ behavior: 'smooth' })"
                class="w-full bg-gradient-to-r from-primary to-tertiary text-white py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap shadow-md font-medium"
              >
                Book Now
              </button>
            </div>
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-card hover:shadow-hover transition-all duration-300 pricing-card"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              <div class="mb-8">
                <h3 class="text-2xl font-bold mb-2 text-left">Beard Trim</h3>
                <div class="text-4xl font-bold gradient-text mb-6 text-left">
                  $10
                </div>
                <ul class="text-gray-600 dark:text-white space-y-4 text-left">
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Professional Beard Trim</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Beard Shaping</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Mustache Trim</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Aftercare Tips</span>
                  </li>
                </ul>
              </div>
              <button
                onclick="document.getElementById('booking').scrollIntoView({ behavior: 'smooth' })"
                class="w-full bg-primary text-white py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap shadow-md font-medium"
              >
                Book Now
              </button>
            </div>
            <div
              class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-card hover:shadow-hover transition-all duration-300 pricing-card"
              data-aos="fade-up"
              data-aos-delay="300"
            >
              <div class="mb-8">
                <h3 class="text-2xl font-bold mb-2 text-left">Clean Shave</h3>
                <div class="text-4xl font-bold gradient-text mb-6 text-left">
                  $5
                </div>
                <ul class="text-gray-600 dark:text-white space-y-4 text-left">
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Modern Machine &amp; Razor Shave</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Pre-shave Oil</span>
                  </li>
                  <li class="flex items-center">
                    <i
                      class="fa-solid fa-check-circle text-primary mr-3 text-lg"
                    ></i>
                    <span>Aftershave Balm</span>
                  </li>
                </ul>
              </div>
              <button
                onclick="document.getElementById('booking').scrollIntoView({ behavior: 'smooth' })"
                class="w-full bg-primary text-white py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap shadow-md font-medium"
              >
                Book Now
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Booking Section -->
      <section
        id="booking"
        class="py-24 bg-gray-50 dark:bg-gray-800 relative overflow-hidden"
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        <!-- Background decoration elements -->
        <div
          class="absolute top-0 right-0 w-64 h-64 bg-primary opacity-5 rounded-full -mr-32 -mt-32"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-96 h-96 bg-tertiary opacity-5 rounded-full -ml-48 -mb-48"
        ></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div class="mb-20 text-center">
            <span
              class="text-sm font-semibold text-primary uppercase tracking-wider"
              >Schedule Now</span
            >
            <h2
              class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mt-2 mb-4"
            >
              Book Your Appointment
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mb-6 rounded-full"></div>
            <p class="text-gray-600 dark:text-white max-w-2xl mx-auto text-lg">
              Schedule your home service in just a few clicks. Our professional
              barbers are ready to serve you.
            </p>
          </div>
          <div class="grid md:grid-cols-3 gap-8 mb-12">
            <!-- Booking-step cards with enhanced styling -->
            <div
              class="booking-step active p-8 rounded-xl shadow-card text-center bg-white dark:bg-gray-700"
            >
              <div
                class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 service-icon-container"
              >
                <i class="fa-solid fa-scissors text-primary text-xl"></i>
              </div>
              <h3 class="text-xl font-bold mb-3">Choose Service</h3>
              <p class="text-gray-600 dark:text-white">
                Select your desired service from our premium offerings
              </p>
            </div>
            <div
              class="booking-step p-8 rounded-xl shadow-card text-center bg-white dark:bg-gray-700"
            >
              <div
                class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 service-icon-container"
              >
                <i class="fa-solid fa-calendar text-primary text-xl"></i>
              </div>
              <h3 class="text-xl font-bold mb-3">Pick Date &amp; Time</h3>
              <p class="text-gray-600 dark:text-white">
                Choose your preferred schedule that works best for you
              </p>
            </div>
            <div
              class="booking-step p-8 rounded-xl shadow-card text-center bg-white dark:bg-gray-700"
            >
              <div
                class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 service-icon-container"
              >
                <i class="fa-solid fa-location-dot text-primary text-xl"></i>
              </div>
              <h3 class="text-xl font-bold mb-3">Your Details</h3>
              <p class="text-gray-600 dark:text-white">
                Confirm your location and contact information
              </p>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-custom">
            <div class="grid md:grid-cols-2 gap-12">
              <!-- Left Column: Service Selection -->
              <div>
                <h3
                  class="text-2xl font-bold mb-8 text-center md:text-left gradient-text"
                >
                  Select Your Service
                </h3>
                <div
                  id="serviceList"
                  class="grid grid-cols-2 gap-4 md:grid-cols-1 md:space-y-4"
                >
                  <!-- Classic Haircut -->
                  <div
                    class="p-6 rounded-xl cursor-pointer hover:shadow-md transition-all duration-300 flex flex-col gap-1 md:flex-row md:items-center md:justify-between border border-gray-100 dark:border-gray-700 hover:border-primary dark:hover:border-primary"
                    data-service="Classic Haircut"
                    data-duration="30"
                    data-price="30"
                    data-aos="fade-up"
                    data-aos-delay="0"
                  >
                    <div>
                      <h4 class="font-bold text-lg">Classic Haircut</h4>
                      <p class="text-sm text-gray-600 dark:text-white">
                        30 mins
                      </p>
                    </div>
                    <div class="flex items-center gap-3 mt-2 md:mt-0">
                      <span class="text-primary font-bold text-lg">$30</span>
                      <div
                        class="w-6 h-6 rounded-full flex items-center justify-center service-radio border-2 border-gray-200 dark:border-gray-600"
                      ></div>
                    </div>
                  </div>
                  <!-- Children's Cut -->
                  <div
                    class="p-6 rounded-xl cursor-pointer hover:shadow-md transition-all duration-300 flex flex-col gap-1 md:flex-row md:items-center md:justify-between border border-gray-100 dark:border-gray-700 hover:border-primary dark:hover:border-primary"
                    data-service="Children's Cut"
                    data-duration="25"
                    data-price="25"
                    data-aos="fade-up"
                    data-aos-delay="100"
                  >
                    <div>
                      <h4 class="font-bold text-lg">Children's Cut</h4>
                      <p class="text-sm text-gray-600 dark:text-white">
                        25 mins
                      </p>
                    </div>
                    <div class="flex items-center gap-3 mt-2 md:mt-0">
                      <span class="text-primary font-bold text-lg">$25</span>
                      <div
                        class="w-6 h-6 rounded-full flex items-center justify-center service-radio border-2 border-gray-200 dark:border-gray-600"
                      ></div>
                    </div>
                  </div>
                  <!-- Beard Trim -->
                  <div
                    class="p-6 rounded-xl cursor-pointer hover:shadow-md transition-all duration-300 flex flex-col gap-1 md:flex-row md:items-center md:justify-between border border-gray-100 dark:border-gray-700 hover:border-primary dark:hover:border-primary"
                    data-service="Beard Trim"
                    data-duration="15"
                    data-price="10"
                    data-aos="fade-up"
                    data-aos-delay="200"
                  >
                    <div>
                      <h4 class="font-bold text-lg">Beard Trim</h4>
                      <p class="text-sm text-gray-600 dark:text-white">
                        15 mins
                      </p>
                    </div>
                    <div class="flex items-center gap-3 mt-2 md:mt-0">
                      <span class="text-primary font-bold text-lg">$10</span>
                      <div
                        class="w-6 h-6 rounded-full flex items-center justify-center service-radio border-2 border-gray-200 dark:border-gray-600"
                      ></div>
                    </div>
                  </div>
                  <!-- Clean Shave -->
                  <div
                    class="p-6 rounded-xl cursor-pointer hover:shadow-md transition-all duration-300 flex flex-col gap-1 md:flex-row md:items-center md:justify-between border border-gray-100 dark:border-gray-700 hover:border-primary dark:hover:border-primary"
                    data-service="Clean Shave"
                    data-duration="10"
                    data-price="5"
                    data-aos="fade-up"
                    data-aos-delay="300"
                  >
                    <div>
                      <h4 class="font-bold text-lg">Clean Shave</h4>
                      <p class="text-sm text-gray-600 dark:text-white">
                        10 mins
                      </p>
                    </div>
                    <div class="flex items-center gap-3 mt-2 md:mt-0">
                      <span class="text-primary font-bold text-lg">$5</span>
                      <div
                        class="w-6 h-6 rounded-full flex items-center justify-center service-radio border-2 border-gray-200 dark:border-gray-600"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Right Column: Appointment & Details -->
              <div>
                <h3
                  class="text-2xl font-bold mb-8 text-center md:text-left gradient-text"
                >
                  Appointment Details
                </h3>
                <div
                  class="bg-gray-50 dark:bg-gray-700 p-8 rounded-xl shadow-sm"
                >
                  <div class="space-y-6">
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                        >Your Name</label
                      >
                      <input
                        type="text"
                        id="customerName"
                        class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white dark:border-gray-600"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                        >Phone Number</label
                      >
                      <input
                        type="tel"
                        id="customerPhone"
                        pattern="[0-9]{3}[0-9]{3}[0-9]{4}"
                        class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white dark:border-gray-600"
                        placeholder="Your phone number (e.g. 1234567890)"
                        required
                      />
                    </div>
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                        >Email Address</label
                      >
                      <input
                        type="email"
                        id="customerEmail"
                        class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white dark:border-gray-600"
                        placeholder="Your email address"
                      />
                    </div>
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                        >Service Address</label
                      >
                      <div class="relative">
                        <input
                          type="text"
                          id="addressInput"
                          class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white dark:border-gray-600"
                          placeholder="Enter your address in Colorado"
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                        >Select Date</label
                      >
                      <input
                        type="date"
                        id="appointmentDate"
                        min=""
                        class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      />
                    </div>
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                        >Select Time</label
                      >
                      <select
                        id="appointmentTime"
                        class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary pr-8 dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      >
                        <option value="">Choose a time slot</option>
                      </select>
                    </div>
                    <button
                      id="bookAppointment"
                      class="w-full bg-gradient-to-r from-primary to-tertiary text-white py-3 rounded-lg mt-6 hover:bg-opacity-90 whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed shadow-md font-medium"
                      disabled
                    >
                      Book Appointment
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <!-- Confirmation Modal -->
            <div
              id="confirmationModal"
              class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50"
            >
              <div
                class="bg-white dark:bg-gray-800 p-8 rounded-xl max-w-md w-full mx-4 shadow-custom"
              >
                <h3 class="text-2xl font-bold mb-4 gradient-text">
                  Confirm Your Appointment
                </h3>
                <div class="space-y-4 mb-6">
                  <p class="text-gray-600 dark:text-white">
                    Please review your appointment details:
                  </p>
                  <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-xl">
                    <div class="space-y-3" id="modalDetails"></div>
                  </div>
                </div>
                <div class="flex gap-4">
                  <button
                    id="confirmBooking"
                    class="flex-1 bg-gradient-to-r from-primary to-tertiary text-white py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap shadow-md font-medium"
                  >
                    Confirm
                  </button>
                  <button
                    id="cancelBooking"
                    class="flex-1 border border-gray-300 dark:border-gray-600 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 whitespace-nowrap font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
            <!-- Success Modal -->
            <div
              id="successModal"
              class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50"
            >
              <div
                class="bg-white dark:bg-gray-800 p-8 rounded-xl max-w-md w-full mx-4 text-center shadow-custom"
              >
                <div
                  class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
                >
                  <i class="fa-solid fa-check text-green-500 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold mb-3">Booking Confirmed!</h3>
                <p class="text-gray-600 dark:text-white mb-8">
                  Your appointment has been successfully scheduled. We'll see
                  you soon!
                </p>
                <button
                  id="closeSuccess"
                  class="w-full bg-gradient-to-r from-primary to-tertiary text-white py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap shadow-md font-medium"
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Testimonials Section with Slick Carousel -->
      <section
        id="testimonials"
        class="py-24 bg-gray-50 dark:bg-gray-800 relative overflow-hidden"
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        <!-- Background decoration elements -->
        <div
          class="absolute top-0 left-0 w-64 h-64 bg-primary opacity-5 rounded-full -ml-32 -mt-32"
        ></div>
        <div
          class="absolute bottom-0 right-0 w-96 h-96 bg-tertiary opacity-5 rounded-full -mr-48 -mb-48"
        ></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div class="text-center mb-20">
            <span
              class="text-sm font-semibold text-primary uppercase tracking-wider"
              >Testimonials</span
            >
            <h2
              class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mt-2 mb-4"
            >
              What Our Clients Say
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mb-6 rounded-full"></div>
            <p class="text-gray-600 dark:text-white max-w-2xl mx-auto text-lg">
              Read what our satisfied customers have to say about their
              experience with our mobile barbering service.
            </p>
          </div>
          <div class="center">
            <!-- Testimonial Card 1 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="0"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <div class="testimonial-text has-tooltip">
                <p class="text-gray-600 dark:text-white">
                  "Exceptional service! The convenience of having a professional
                  barber come to my home is unmatched. The attention to detail
                  and professionalism exceeded my expectations. I've never had a
                  better haircut experience."
                </p>
                <div class="testimonial-tooltip">
                  "Exceptional service! The convenience of having a professional
                  barber come to my home is unmatched. The attention to detail
                  and professionalism exceeded my expectations. I've never had a
                  better haircut experience."
                </div>
              </div>
              <div class="testimonial-author">
                <h4 class="font-bold">Michael Anderson</h4>
                <span class="text-gray-400">•</span>
                <p class="text-sm text-gray-600 dark:text-white">
                  Regular Client
                </p>
              </div>
            </div>
            <!-- Testimonial Card 2 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="100"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "The attention to detail and professionalism is impressive. I'll
                definitely be a repeat customer!"
              </p>
              <div>
                <h4 class="font-bold">James Wilson</h4>
                <p class="text-sm dark:text-white">Monthly Subscriber</p>
              </div>
            </div>
            <!-- Testimonial Card 3 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "Best grooming experience I've had. Consistently high quality
                service every time."
              </p>
              <div>
                <h4 class="font-bold">Robert Thompson</h4>
                <p class="text-sm dark:text-white">Premium Member</p>
              </div>
            </div>
            <!-- Testimonial Card 4 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="300"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "I was impressed by the punctuality and friendly service. Highly
                recommended!"
              </p>
              <div>
                <h4 class="font-bold">Lisa Smith</h4>
                <p class="text-sm dark:text-white">New Client</p>
              </div>
            </div>
            <!-- Testimonial Card 5 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="400"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "Absolutely loved the service. Highly recommend to everyone
                looking for quality haircuts."
              </p>
              <div>
                <h4 class="font-bold">David Johnson</h4>
                <p class="text-sm dark:text-white">Loyal Customer</p>
              </div>
            </div>
            <!-- Testimonial Card 6 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="500"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "A game changer in mobile grooming services. So convenient and
                professional."
              </p>
              <div>
                <h4 class="font-bold">Sophia Lee</h4>
                <p class="text-sm dark:text-white">Satisfied Customer</p>
              </div>
            </div>
            <!-- Testimonial Card 7 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="600"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "I enjoyed the seamless booking process and the quality service.
                Will book again!"
              </p>
              <div>
                <h4 class="font-bold">Mark Davis</h4>
                <p class="text-sm dark:text-white">Frequent Client</p>
              </div>
            </div>
            <!-- Testimonial Card 8 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="700"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "Top-notch grooming experience. I'll definitely book again
                soon."
              </p>
              <div>
                <h4 class="font-bold">Emily Clark</h4>
                <p class="text-sm dark:text-white">Returning Customer</p>
              </div>
            </div>
            <!-- Testimonial Card 9 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="800"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "Efficient and professional service every time. Highly satisfied
                with the results."
              </p>
              <div>
                <h4 class="font-bold">Chris Martin</h4>
                <p class="text-sm dark:text-white">Regular Client</p>
              </div>
            </div>
            <!-- Testimonial Card 10 -->
            <div
              class="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-card transition-all duration-300 testimonial-card text-center"
              data-aos="fade-up"
              data-aos-delay="900"
            >
              <div class="flex justify-center mb-4">
                <div class="text-yellow-400 flex">
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                  <i class="fa-solid fa-star"></i>
                </div>
              </div>
              <p class="text-gray-600 dark:text-white mb-6">
                "The service exceeded all my expectations. Perfect haircut every
                time!"
              </p>
              <div>
                <h4 class="font-bold">Anna Brown</h4>
                <p class="text-sm dark:text-white">Happy Client</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section
        id="contact"
        class="py-24 bg-white dark:bg-gray-900 relative overflow-hidden"
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        <!-- Background decoration elements -->
        <div
          class="absolute top-0 right-0 w-96 h-96 bg-primary opacity-5 rounded-full -mr-48 -mt-48"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-64 h-64 bg-tertiary opacity-5 rounded-full -ml-32 -mb-32"
        ></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div class="text-center mb-20">
            <span
              class="text-sm font-semibold text-primary uppercase tracking-wider"
              >Get In Touch</span
            >
            <h2
              class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mt-2 mb-4"
            >
              Contact Us
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mb-6 rounded-full"></div>
            <p class="text-gray-600 dark:text-white max-w-2xl mx-auto text-lg">
              Have questions? Get in touch with us. We're here to help you with
              any inquiries about our mobile barbering service.
            </p>
          </div>
          <div
            class="grid md:grid-cols-2 gap-12 justify-items-center md:justify-items-start"
          >
            <div class="mx-auto w-full max-w-md">
              <form class="space-y-6 text-center md:text-left">
                <div class="mx-auto md:mx-0">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                    >Name</label
                  >
                  <input
                    type="text"
                    class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm mx-auto md:mx-0"
                    placeholder="Your name"
                  />
                </div>
                <div class="mx-auto md:mx-0">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                    >Email</label
                  >
                  <input
                    type="email"
                    class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm mx-auto md:mx-0"
                    placeholder="Your email"
                  />
                </div>
                <div class="mx-auto md:mx-0">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-white mb-2"
                    >Message</label
                  >
                  <textarea
                    class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary h-32 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm mx-auto md:mx-0"
                    placeholder="Your message"
                  ></textarea>
                </div>
                <button
                  class="w-full bg-gradient-to-r from-primary to-tertiary text-white py-3 rounded-lg hover:bg-opacity-90 whitespace-nowrap shadow-md font-medium transition-all duration-300 hover:scale-[1.02] mx-auto md:mx-0"
                >
                  Send Message
                </button>
              </form>
            </div>
            <div class="mx-auto w-full max-w-md">
              <div
                class="bg-gray-50 dark:bg-gray-800 p-8 rounded-xl shadow-card h-full"
              >
                <div class="space-y-8">
                  <div>
                    <h3
                      class="text-2xl font-bold mb-6 gradient-text text-center sm:text-center md:text-left"
                    >
                      Service Areas in Colorado
                    </h3>
                    <div
                      class="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden shadow-md"
                    >
                      <img
                        src="https://geology.com/state-map/maps/colorado-county-map.gif"
                        alt="Colorado service area map"
                        class="w-full h-full object-cover"
                      />
                    </div>
                    <div class="mt-6 text-gray-600 dark:text-white">
                      <p class="mb-4 text-center sm:text-center md:text-left">
                        We proudly serve the following areas in Colorado:
                      </p>
                      <ul class="grid grid-cols-2 gap-3">
                        <li class="flex items-center">
                          <i class="fa-solid fa-map-pin text-primary mr-2"></i>
                          Denver Metro Area
                        </li>
                        <li class="flex items-center">
                          <i class="fa-solid fa-map-pin text-primary mr-2"></i>
                          Boulder
                        </li>
                        <li class="flex items-center">
                          <i class="fa-solid fa-map-pin text-primary mr-2"></i>
                          Fort Collins
                        </li>
                        <li class="flex items-center">
                          <i class="fa-solid fa-map-pin text-primary mr-2"></i>
                          Colorado Springs
                        </li>
                        <li class="flex items-center">
                          <i class="fa-solid fa-map-pin text-primary mr-2"></i>
                          Aurora
                        </li>
                        <li class="flex items-center">
                          <i class="fa-solid fa-map-pin text-primary mr-2"></i>
                          Lakewood
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div>
                    <h3
                      class="text-2xl font-bold mb-6 gradient-text text-center sm:text-center md:text-left"
                    >
                      Contact Information
                    </h3>
                    <div class="space-y-5">
                      <div
                        class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm"
                      >
                        <div
                          class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-4"
                        >
                          <i class="fa-solid fa-envelope text-primary"></i>
                        </div>
                        <span class="font-medium"
                          ><EMAIL></span
                        >
                      </div>
                      <div
                        class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm"
                      >
                        <div
                          class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-4"
                        >
                          <i class="fa-solid fa-clock text-primary"></i>
                        </div>
                        <span class="font-medium"
                          >Mon-Sat: 8:00 AM - 8:00 PM MT</span
                        >
                      </div>
                      <div
                        class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm"
                      >
                        <div
                          class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-4"
                        >
                          <i class="fa-solid fa-location-dot text-primary"></i>
                        </div>
                        <span class="font-medium"
                          >Based in Denver, Colorado</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer
      class="bg-gray-900 dark:bg-gray-800 text-white py-16 relative overflow-hidden"
    >
      <!-- Background decoration elements -->
      <div
        class="absolute top-0 right-0 w-64 h-64 bg-primary opacity-5 rounded-full -mr-32 -mt-32"
      ></div>
      <div
        class="absolute bottom-0 left-0 w-96 h-96 bg-tertiary opacity-5 rounded-full -ml-48 -mb-48"
      ></div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="grid md:grid-cols-4 gap-12">
          <div>
            <a
              href="#home"
              class="logo text-3xl text-white mb-6 block font-bold"
              >The Home Barber</a
            >
            <p class="text-gray-400 mb-6">
              The home barbering services bringing style and convenience to your
              doorstep throughout Colorado.
            </p>
            <div class="flex space-x-4">
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-colors duration-300 bg-gray-800 hover:bg-primary p-3 rounded-full"
              >
                <i class="fa-brands fa-facebook-f text-lg"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-colors duration-300 bg-gray-800 hover:bg-primary p-3 rounded-full"
              >
                <i class="fa-brands fa-instagram text-lg"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-colors duration-300 bg-gray-800 hover:bg-primary p-3 rounded-full"
              >
                <i class="fa-brands fa-twitter text-lg"></i>
              </a>
            </div>
          </div>
          <div>
            <h4 class="text-xl font-bold mb-6">Quick Links</h4>
            <ul class="space-y-4">
              <li>
                <a
                  href="#home"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Home
                </a>
              </li>
              <li>
                <a
                  href="#services"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Services
                </a>
              </li>
              <li>
                <a
                  href="#pricing"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Pricing
                </a>
              </li>
              <li>
                <a
                  href="#contact"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Contact
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h4 class="text-xl font-bold mb-6">Services</h4>
            <ul class="space-y-4">
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Classic Haircut
                </a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Beard Grooming
                </a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Premium Package
                </a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white flex items-center"
                >
                  <i
                    class="fa-solid fa-chevron-right mr-2 text-xs text-primary"
                  ></i>
                  Kids Haircut
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h4 class="text-xl font-bold mb-6">Contact Info</h4>
            <ul class="space-y-4">
              <li class="flex items-center">
                <div
                  class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center mr-3"
                >
                  <i class="fa-solid fa-envelope text-primary"></i>
                </div>
                <span><EMAIL></span>
              </li>
              <li class="flex items-center">
                <div
                  class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center mr-3"
                >
                  <i class="fa-solid fa-location-dot text-primary"></i>
                </div>
                <span>Denver, Colorado</span>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400"
        >
          <p>&copy; 2025 The Home Barber. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <script>
      // Initialize dark mode based on user preference or system setting
      if (
        localStorage.getItem("theme") === "dark" ||
        (!localStorage.getItem("theme") &&
          window.matchMedia("(prefers-color-scheme: dark)").matches)
      ) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }

      // Toggle dark mode and update localStorage and toggle icon.
      function toggleDarkMode() {
        document.documentElement.classList.toggle("dark");
        const toggleIcon = document.getElementById("darkModeIcon");
        const toggleIconMobile = document.getElementById("darkModeIconMobile");
        if (document.documentElement.classList.contains("dark")) {
          localStorage.setItem("theme", "dark");
          toggleIcon.className = "fa-solid fa-sun text-xl";
          toggleIconMobile.className = "fa-solid fa-sun text-xl";
        } else {
          localStorage.setItem("theme", "light");
          toggleIcon.className = "fa-solid fa-moon text-xl";
          toggleIconMobile.className = "fa-solid fa-moon text-xl";
        }
      }

      // Toggle mobile menu when hamburger icon is clicked
      document
        .getElementById("mobileMenuButton")
        .addEventListener("click", function () {
          const mobileMenu = document.getElementById("mobileMenu");
          const expanded =
            this.getAttribute("aria-expanded") === "true" || false;
          this.setAttribute("aria-expanded", !expanded);
          mobileMenu.classList.toggle("hidden");
        });

      document.addEventListener("DOMContentLoaded", function () {
        const addressInput = document.getElementById("addressInput");
        const addressSuggestions =
          document.getElementById("addressSuggestions");
        const customerName = document.getElementById("customerName");
        const customerPhone = document.getElementById("customerPhone");
        const customerEmail = document.getElementById("customerEmail");
        const appointmentDate = document.getElementById("appointmentDate");
        const appointmentTime = document.getElementById("appointmentTime");
        const bookAppointmentBtn = document.getElementById("bookAppointment");
        const confirmationModal = document.getElementById("confirmationModal");
        const successModal = document.getElementById("successModal");
        const modalDetails = document.getElementById("modalDetails");
        const serviceCards = document.querySelectorAll("#serviceList > div");

        let selectedService = null;
        const mockAddresses = [
          "123 Main Street, Denver, CO 80202",
          "456 Pearl Street, Boulder, CO 80302",
          "789 College Avenue, Fort Collins, CO 80524",
          "321 Tejon Street, Colorado Springs, CO 80903",
          "654 Havana Street, Aurora, CO 80010",
          "987 Wadsworth Boulevard, Lakewood, CO 80226",
          "741 University Avenue, Denver, CO 80210",
          "852 Broadway Street, Boulder, CO 80304",
          "963 Mountain Avenue, Fort Collins, CO 80521",
          "159 Nevada Avenue, Colorado Springs, CO 80903",
          "753 Colfax Avenue, Denver, CO 80204",
          "951 Lincoln Street, Brighton, CO 80601",
          "357 Washington Street, Golden, CO 80401",
          "846 Market Street, Englewood, CO 80110",
          "264 Union Boulevard, Lakewood, CO 80228",
        ];

        const today = new Date();
        appointmentDate.min = today.toISOString().split("T")[0];

        function generateTimeSlots() {
          appointmentTime.innerHTML =
            '<option value="">Choose a time slot</option>';
          const slots = [];
          for (let hour = 8; hour < 20; hour++) {
            for (let minute of ["00", "30"]) {
              slots.push(`${hour.toString().padStart(2, "0")}:${minute}`);
            }
          }
          slots.forEach((slot) => {
            const option = document.createElement("option");
            option.value = slot;
            option.textContent = slot;
            appointmentTime.appendChild(option);
          });
        }
        generateTimeSlots();

        // Enhanced service card selection with animation
        serviceCards.forEach((card) => {
          card.addEventListener("click", () => {
            serviceCards.forEach((c) => {
              c.classList.remove("border-primary", "bg-primary/5");
              c.querySelector(".service-radio").innerHTML = "";
            });
            card.classList.add("border-primary", "bg-primary/5");
            card.querySelector(".service-radio").innerHTML =
              '<div class="w-3 h-3 bg-primary rounded-full"></div>';
            selectedService = {
              name: card.dataset.service,
              duration: parseInt(card.dataset.duration),
              price: parseInt(card.dataset.price),
            };
            updateSummary();
          });
        });

        function updateSummary() {
          const nameVal = customerName.value;
          const phoneVal = customerPhone.value;
          const emailVal = customerEmail.value;
          const addressVal = addressInput.value;
          const dateVal = appointmentDate.value;
          const timeVal = appointmentTime.value;
          const allFilled =
            selectedService &&
            dateVal &&
            timeVal &&
            addressVal &&
            nameVal &&
            phoneVal &&
            emailVal;
          bookAppointmentBtn.disabled = !allFilled;
        }

        [
          customerName,
          customerPhone,
          customerEmail,
          addressInput,
          appointmentDate,
          appointmentTime,
        ].forEach((input) => {
          input.addEventListener("input", updateSummary);
          input.addEventListener("change", updateSummary);
        });

        bookAppointmentBtn.addEventListener("click", () => {
          if (!selectedService) {
            modalDetails.innerHTML = `<p>Please select a service first.</p>`;
            confirmationModal.style.display = "flex";
            return;
          }
          const details = `
            <p class="mb-2"><strong>Customer:</strong> ${customerName.value}</p>
            <p class="mb-2"><strong>Phone:</strong> ${customerPhone.value}</p>
            <p class="mb-2"><strong>Email:</strong> ${customerEmail.value}</p>
            <p class="mb-2"><strong>Address:</strong> ${addressInput.value}</p>
            <p class="mb-2"><strong>Date & Time:</strong> ${appointmentDate.value} at ${appointmentTime.value}</p>
            <p class="mb-2"><strong>Service:</strong> ${selectedService.name}</p>
            <p class="mb-2"><strong>Duration:</strong> ${selectedService.duration} mins</p>
            <p class="mb-2"><strong>Total:</strong> ${selectedService.price}</p>
          `;
          modalDetails.innerHTML = details;
          confirmationModal.style.display = "flex";
        });

        document
          .getElementById("confirmBooking")
          .addEventListener("click", () => {
            confirmationModal.style.display = "none";
            successModal.style.display = "flex";
          });
        document
          .getElementById("cancelBooking")
          .addEventListener("click", () => {
            confirmationModal.style.display = "none";
          });
        document
          .getElementById("closeSuccess")
          .addEventListener("click", () => {
            successModal.style.display = "none";
            serviceCards.forEach((card) => {
              card.classList.remove("border-primary", "bg-primary/5");
              card.querySelector(".service-radio").innerHTML = "";
            });
            selectedService = null;
            appointmentDate.value = "";
            appointmentTime.value = "";
            addressInput.value = "";
            customerName.value = "";
            customerPhone.value = "";
            customerEmail.value = "";
            updateSummary();
          });

        // Smooth scrolling for navigation links
        const navLinks = document.querySelectorAll('a[href^="#"]');
        navLinks.forEach((link) => {
          link.addEventListener("click", function (e) {
            e.preventDefault();
            const targetId = this.getAttribute("href");
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
              targetElement.scrollIntoView({ behavior: "smooth" });
              // Close mobile menu if open
              if (
                !document
                  .getElementById("mobileMenu")
                  .classList.contains("hidden")
              ) {
                document.getElementById("mobileMenu").classList.add("hidden");
                document
                  .getElementById("mobileMenuButton")
                  .setAttribute("aria-expanded", "false");
              }
            }
          });
        });
      });

      // Initialize Slick Carousel for testimonials with enhanced settings
      $(document).ready(function () {
        $(".center").slick({
          centerMode: true,
          centerPadding: "60px",
          slidesToShow: 3,
          slidesToScroll: 1,
          autoplay: true,
          autoplaySpeed: 3000,
          dots: true,
          arrows: true,
          responsive: [
            {
              breakpoint: 1024,
              settings: {
                slidesToShow: 2,
                centerMode: false,
              },
            },
            {
              breakpoint: 768,
              settings: {
                slidesToShow: 1,
                centerMode: true,
                centerPadding: "40px",
              },
            },
          ],
        });
      });
    </script>
    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
      // Initialize AOS with custom settings
      AOS.init({
        once: true,
        offset: 100,
        duration: 800,
        easing: "ease-in-out",
        delay: 100,
      });
    </script>
  </body>
</html>
